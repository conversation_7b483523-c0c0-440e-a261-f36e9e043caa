from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, Integer

from gc_dentist_shared.base.datetime_mixin import DateTimeMixin
from gc_dentist_shared.base.tenant_declarative_base import TenantBase


class DocumentTag(TenantBase, DateTimeMixin):
    __tablename__ = "document_tags"

    id = Column(Integer, primary_key=True, autoincrement=True)
    document_management_id = Column(
        Integer, ForeignKey("document_managements.id"), nullable=False
    )
    tag_id = Column(Integer, ForeignKey("tags.id"), nullable=False)
