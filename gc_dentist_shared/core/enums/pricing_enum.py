from enum import Enum
from typing import NamedTuple

from enums.base import IntEnum


class TenantPricingStorageKey(IntEnum):
    BASIC = 2
    PREMIUM = 4
    ENTERPRISE = 8


class StorageStatus(NamedTuple):
    status_id: int
    name: str


class TenantExtraStorageStatus(Enum):
    PENDING = StorageStatus(1, "Pending")
    ACTIVE = StorageStatus(3, "Active")
    EXPIRED = StorageStatus(5, "Expired")
    CANCELLED = StorageStatus(7, "Cancelled")

    @property
    def status_id(self):
        return self.value.status_id

    @property
    def name(self):
        return self.value.name
