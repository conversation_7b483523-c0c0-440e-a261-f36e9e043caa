"""Init model Tag

Revision ID: 4a5e1faca478
Revises: c1f2ab472d81
Create Date: 2025-09-26 11:27:13.644853

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "4a5e1faca478"
down_revision: Union[str, None] = "c1f2ab472d81"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "tags",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column(
            "description", sa.String(), nullable=True, comment="Description of the tag"
        ),
        sa.Column("is_active", sa.<PERSON>(), nullable=False),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
    )
    op.create_table(
        "document_tags",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("document_management_id", sa.Integer(), nullable=False),
        sa.Column("tag_id", sa.Integer(), nullable=False),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("updated_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.Column("deleted_at", sa.TIMESTAMP(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["document_management_id"],
            ["document_managements.id"],
        ),
        sa.ForeignKeyConstraint(
            ["tag_id"],
            ["tags.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.alter_column(
        "patient_profiles",
        "full_name_kana",
        existing_type=sa.VARCHAR(),
        comment="Full Name in Kana from Middleware Application",
        existing_comment="Full Name in Kanna from Middleware Application",
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "patient_profiles",
        "full_name_kana",
        existing_type=sa.VARCHAR(),
        comment="Full Name in Kanna from Middleware Application",
        existing_comment="Full Name in Kana from Middleware Application",
        existing_nullable=True,
    )
    op.drop_table("document_tags")
    op.drop_table("tags")
    # ### end Alembic commands ###
