import json
from datetime import datetime

from core.common.api_response import <PERSON>piResponse
from core.messages import CustomMessageCode
from fastapi import Request, status
from fastapi.exceptions import RequestValidationError
from pydantic_core._pydantic_core import ValidationError

from gc_dentist_shared.core.logger.config import log


def safe_serialize(obj):
    """Safely serialize objects that might not be JSON serializable."""
    if isinstance(obj, datetime):
        return obj.isoformat()
    return str(obj)


def pydantic_validation_exception_handler(
    request: Request, exc: RequestValidationError | ValidationError
):
    error_messages = []
    for error in exc.errors():
        field_path = ".".join(str(loc) for loc in error["loc"])
        message = error["msg"]
        error_messages.append(f"Field Path '{field_path}': {message}")

    log.error(
        f"❌ Validation error for {request.method} {request.url}: {error_messages}"
    )

    try:
        errors_data_json = json.dumps(exc.errors(), default=safe_serialize)
        errors_data = json.loads(errors_data_json)
    except Exception as serialization_exc:
        log.error(f"❌ Serialization error for validation errors: {serialization_exc}")
        errors_data = None

    return ApiResponse.error(
        message=CustomMessageCode.INVALID_REQUEST_PAYLOAD.title,
        message_code=CustomMessageCode.INVALID_REQUEST_PAYLOAD.code,
        data=errors_data,
        message_errors=error_messages,
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
    )