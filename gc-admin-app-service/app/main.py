import gettext
from contextlib import asynccontextmanager

from api.routes import router
from configuration.handler.exception_handler import (
    pydantic_validation_exception_handler,
)
from configuration.middleware.jwt_auth_middleware import JWTAuthMiddleware
from configuration.middleware.locale_i18n_middleware import LanguageMiddleware
from configuration.settings import Settings, configuration
from core.common.api_response import ApiResponse
from core.initialize_function import firebase_init
from db.db_connection import CentralDatabase
from fastapi import FastAPI
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi_pagination import add_pagination
from pydantic_core._pydantic_core import ValidationError
from sqlalchemy import text
from starlette.middleware.authentication import AuthenticationMiddleware
from starlette_context.middleware import RawContextMiddleware

from gc_dentist_shared.core.common.aes_gcm import AesGCMRotation
from gc_dentist_shared.core.common.jwks_client_manager import JWKSClientManager
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.hooks.dangerous_sql import (  # noqa: F401
    prevent_dangerous_sql,
)
from gc_dentist_shared.core.logger.config import log


def create_app(skip_auth: bool = False) -> FastAPI:
    @asynccontextmanager
    async def lifespan(app: FastAPI):
        # startup logic
        await load_aes_key()
        init_translations()
        register_jwks(configuration)

        # Connect firebase
        if configuration.FIREBASE_ENABLED:
            firebase_init()

        session = CentralDatabase.get_sessionmaker()
        try:
            async with session() as conn:
                await conn.execute(text("SELECT 1"))
            log.info("✅ Database connected successfully")
        except Exception as e:
            log.error(f"❌ Database connection failed: {e}")
            raise e
        yield

    app = FastAPI(lifespan=lifespan)
    app.include_router(router)
    add_pagination(app)

    register_middlewares(app)
    register_exception(app)
    return app


def register_jwks(configuration):
    JWKSClientManager.get_instance(configuration)
    log.info("✅ JWKS client initialized")


def register_middlewares(app: FastAPI):
    env = Settings().ENVIRONMENT
    if env in ["production", "staging"]:
        from configuration.middleware.opentelemetry_log_middleware import (
            OpenTelemetryLogMiddleware,
        )

        app.add_middleware(OpenTelemetryLogMiddleware)

    app.add_middleware(
        AuthenticationMiddleware,
        backend=JWTAuthMiddleware(),
        on_error=JWTAuthMiddleware.auth_exception_handler,
    )
    app.add_middleware(RawContextMiddleware)
    app.add_middleware(LanguageMiddleware)
    app.add_middleware(
        CORSMiddleware,
        allow_origins=configuration.BACKEND_CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=configuration.BACKEND_CORS_METHODS,
        allow_headers=["*"],
    )


def register_exception(app: FastAPI):
    app.add_exception_handler(
        RequestValidationError, pydantic_validation_exception_handler
    )
    app.add_exception_handler(ValidationError, pydantic_validation_exception_handler)

    # Handle CustomValueError to return proper ApiResponse format
    @app.exception_handler(CustomValueError)
    def custom_value_error_handler(request, exc: CustomValueError):
        return ApiResponse.error(
            message=exc.message,
            message_code=exc.message_code,
            status_code=exc.status_code,
        )


async def load_aes_key():
    env = Settings().ENVIRONMENT
    if env in ["production", "staging"]:
        (
            aws_secret_rotation_key_mapping,
            current_version_id,
        ) = await AesGCMRotation(
            configuration=configuration
        ).get_key_from_aws_secrets_manager(Settings().AES_SECRET_ID_ROTATION)

        if aws_secret_rotation_key_mapping:
            Settings().AWS_SECRET_ROTATION_KEY_MAPPING = aws_secret_rotation_key_mapping

    if not Settings().AWS_SECRET_CURRENT_VERSION:
        log.error("❌ AES ENV secret get current version is None")
        return

    if not Settings().AWS_SECRET_ROTATION_KEY_MAPPING:
        log.error("❌ AES ENV secret key is None")
        return


def init_translations():
    from pathlib import Path

    from gc_dentist_shared.core.common.i18n import i18n

    base_dir = Path(__file__).resolve().parent

    i18n.load_translations(
        {
            "ja_JP": gettext.translation(
                domain="messages",
                localedir=base_dir / "locale",
                languages=["ja_JP"],
            ),
            "en_US": gettext.translation(
                domain="messages",
                localedir=base_dir / "locale",
                languages=["en_US"],
            ),
        }
    )


app = create_app(skip_auth=True)
