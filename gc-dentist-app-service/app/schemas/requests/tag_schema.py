from typing import Optional

from pydantic import BaseModel, Field, field_validator


class CreateTagPayload(BaseModel):
    name: str = Field(..., min_length=1, description="Name of the tag")
    description: Optional[str] = Field(
        default=None, description="Description of the tag"
    )

    @field_validator("name")
    @classmethod
    def normalize_name(cls, v: str) -> str:
        return v.strip().lower()


class UpdateTagPayload(BaseModel):
    name: Optional[str] = Field(
        default=None, min_length=1, description="Name of the tag"
    )
    description: Optional[str] = Field(
        default=None, description="Description of the tag"
    )

    @field_validator("name")
    @classmethod
    def normalize_name(cls, v: str) -> str:
        return v.strip().lower()
