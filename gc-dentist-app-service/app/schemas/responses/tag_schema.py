from typing import Optional

from pydantic import BaseModel, Field, field_validator


class TagResponseSchema(BaseModel):
    id: int = Field(..., description="Primary key ID")
    name: str = Field(..., description="Name of the tag")
    description: Optional[str] = Field(
        default=None, description="Description of the tag"
    )

    @field_validator("name")
    @classmethod
    def to_uppercase(cls, v: str) -> str:
        return v.upper()
