from typing import Annotated

from core.common.api_response import ApiResponse
from core.constants import X_TENANT_UUID
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends, Request
from schemas.requests.auth_schema import (
    S3GeneratedPresignedUrlRequest,
    S3LocationRequest,
)
from services.auth_s3_service import S3AuthService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.api_version import version
from gc_dentist_shared.core.exception_handler.custom_exception import (
    CustomValueError,
    S3BucketExceptionError,
)
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.post(path="/generate-presigned-url", dependencies=[])
@version(1, 0)
async def generate_presigned_url(
    obj: S3GeneratedPresignedUrlRequest,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
    request: Request,
):
    """
    Generate a presigned URL for S3 object access.
    """
    try:
        tenant_uuid = request.headers.get(X_TENANT_UUID)
        service = S3AuthService()
        presigned_urls = await service.generate_presigned_url(
            db_session=db_session,
            obj=obj,
            tenant_uuid=tenant_uuid,
        )
        return ApiResponse.success(
            data=presigned_urls,
        )
    except (S3BucketExceptionError, CustomValueError) as e:
        log.error(f"❌ Error: {str(e)}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error API generating presigned URL: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
        )


@router.post(path="/generate-presigned-url-by-s3-location")
@version(1, 0)
async def generate_presigned_url_by_s3_location(
    obj: S3LocationRequest,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    """
    Generate a presigned URL for S3 object access by s3 location.
    """
    try:
        service = S3AuthService()
        presigned_url = await service.generate_presigned_url_by_s3_location(
            session=db_session,
            obj=obj,
        )
        return ApiResponse.success(
            data=presigned_url,
        )
    except (S3BucketExceptionError, CustomValueError) as e:
        log.error(f"❌ Error: {str(e)}")
        return ApiResponse.error(
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error API generating presigned URL : {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.UNKNOWN_ERROR.title,
            message_code=CustomMessageCode.UNKNOWN_ERROR.code,
        )
