from typing import Annotated, Optional

from core.common.api_response import <PERSON><PERSON><PERSON><PERSON>ponse
from core.messages import CustomMessageCode
from db.db_connection import TenantDatabase
from fastapi import APIRouter, Depends
from fastapi_pagination import Page
from schemas.requests.tag_schema import CreateTagPayload
from schemas.responses.tag_schema import TagResponseSchema
from services.tags.tag_service import TagService
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.core.logger.config import log

router = APIRouter()


@router.get("")
async def search_tags(
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
    search: Optional[str] = None,
) -> Page[TagResponseSchema]:
    try:
        tag_service = TagService(db_session)
        tags = await tag_service.search_tags(search=search)
        return ApiResponse.success(data=tags.model_dump(mode="json"))
    except CustomValueError as e:
        log.error(
            f"❌ Error search_tags CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error in method search_tags: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.TAG_GET_LIST_FAILED.title,
            message_code=CustomMessageCode.TAG_GET_LIST_FAILED.code,
        )


@router.post("")
async def create_tag(
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
    payload: CreateTagPayload,
):
    try:
        tag_service = TagService(db_session)
        tag_id = await tag_service.create_tag(payload)
        return ApiResponse.success(data={"tag_id": tag_id})
    except CustomValueError as e:
        log.error(
            f"❌ Error create_tag CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error in method create_tag: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.TAG_CREATE_FAILED.title,
            message_code=CustomMessageCode.TAG_CREATE_FAILED.code,
        )


@router.put("/{tag_id}")
async def update_tag(
    tag_id: int,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
    payload: CreateTagPayload,
):
    try:
        tag_service = TagService(db_session)
        tag_id = await tag_service.update_tag(tag_id, payload)
        return ApiResponse.success(data={"tag_id": tag_id})
    except CustomValueError as e:
        log.error(
            f"❌ Error update_tag CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error in method update_tag: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.TAG_UPDATE_FAILED.title,
            message_code=CustomMessageCode.TAG_UPDATE_FAILED.code,
        )


@router.delete("/{tag_id}")
async def delete_tag(
    tag_id: int,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        tag_service = TagService(db_session)
        await tag_service.delete_tag(tag_id)
        return ApiResponse.success()
    except CustomValueError as e:
        log.error(
            f"❌ Error delete_tag CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error in method delete_tag: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.TAG_DELETE_FAILED.title,
            message_code=CustomMessageCode.TAG_DELETE_FAILED.code,
        )


@router.get("/{tag_id}")
async def get_tag_by_id(
    tag_id: int,
    db_session: Annotated[AsyncSession, Depends(TenantDatabase.get_tenant_db_session)],
):
    try:
        tag_service = TagService(db_session)
        tag = await tag_service.get_tag_by_id(tag_id)
        return ApiResponse.success(data=tag.model_dump(mode="json"))
    except CustomValueError as e:
        log.error(
            f"❌ Error get_tag_by_id CustomValueException: {e.status_code} - {e.message}"
        )
        return ApiResponse.error(
            status_code=e.status_code,
            message=e.message,
            message_code=e.message_code,
        )
    except Exception as e:
        log.error(f"❌ Error in method get_tag_by_id: {str(e)}")
        return ApiResponse.error(
            message=CustomMessageCode.TAG_GET_DETAIL_FAILED.title,
            message_code=CustomMessageCode.TAG_GET_DETAIL_FAILED.code,
        )
