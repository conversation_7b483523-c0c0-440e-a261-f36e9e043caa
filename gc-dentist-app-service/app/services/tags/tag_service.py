from core.messages import CustomMessageCode
from fastapi_pagination import Page
from fastapi_pagination.ext.sqlalchemy import paginate
from schemas.requests.tag_schema import CreateTagPayload
from schemas.responses.tag_schema import TagResponseSchema
from sqlalchemy import or_, select
from sqlalchemy.exc import <PERSON><PERSON><PERSON><PERSON>r, OperationalError
from sqlalchemy.ext.asyncio import AsyncSession

from gc_dentist_shared.core.decorators.retry import retry_on_failure
from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError
from gc_dentist_shared.tenant_models import Tag


class TagService:
    def __init__(self, session: AsyncSession):
        self.session = session

    async def search_tags(self, search: str | None = None) -> Page[TagResponseSchema]:
        conditions = [Tag.is_active.is_(True)]

        if search:
            search_pattern = f"%{search}%"
            conditions.append(
                or_(
                    Tag.name.ilike(search_pattern),
                    Tag.description.ilike(search_pattern),
                )
            )

        query = select(Tag).where(*conditions)
        return await paginate(self.session, query, unique=False)

    async def get_tag_by_id(self, tag_id: int):
        conditions = [Tag.id == tag_id, Tag.is_active.is_(True)]
        tag = await self._get_tag_by_conditions(conditions)

        if not tag:
            raise CustomValueError(
                message_code=CustomMessageCode.TAG_NOT_FOUND.code,
                message=CustomMessageCode.TAG_NOT_FOUND.title,
            )
        tag_dict = tag.__dict__
        return TagResponseSchema(**tag_dict)

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="create_tag",
    )
    async def create_tag(self, payload: CreateTagPayload):
        async with self.session.begin():
            conditions = [Tag.name == payload.name, Tag.is_active.is_(True)]
            is_tag_exists = await self._get_tag_by_conditions(conditions)
            if is_tag_exists:
                raise CustomValueError(
                    message_code=CustomMessageCode.TAG_EXISTS.code,
                    message=CustomMessageCode.TAG_EXISTS.title,
                )

            tag = Tag(
                name=payload.name,
                description=payload.description,
            )
            self.session.add(tag)
            await self.session.flush()
            return tag.id

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="update_tag",
    )
    async def update_tag(self, tag_id: int, payload: CreateTagPayload):
        async with self.session.begin():
            # Check if tag name exists
            conditions = [Tag.name == payload.name, Tag.is_active.is_(True)]
            is_tag_exists = await self._get_tag_by_conditions(conditions)
            if is_tag_exists:
                raise CustomValueError(
                    message_code=CustomMessageCode.TAG_EXISTS.code,
                    message=CustomMessageCode.TAG_EXISTS.title,
                )

            tag = await self.get_tag_by_id(tag_id)
            tag.name = payload.name
            tag.description = payload.description
            await self.session.flush()
            return tag.id

    @retry_on_failure(
        exceptions=(
            OperationalError,
            DBAPIError,
        ),
        log_prefix="delete_tag",
    )
    async def delete_tag(self, tag_id: int):
        async with self.session.begin():
            tag = await self.get_tag_by_id(tag_id)
            tag.is_active = False
            await self.session.flush()

    # region Private Methods
    async def _get_tag_by_conditions(self, conditions: list):
        query = select(Tag).where(*conditions)
        result = await self.session.execute(query)
        return result.scalar_one_or_none()

    # endregion
