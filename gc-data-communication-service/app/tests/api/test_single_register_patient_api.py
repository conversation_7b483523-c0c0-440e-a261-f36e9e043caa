import pytest
import uuid
from datetime import date, datetime
from unittest.mock import AsyncMock, patch, MagicMock

from fastapi import status
from httpx import AsyncClient
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from core.messages import CustomMessageCode
from enums.patient_enum import PatientColumnMapping
from schemas.requests.patient_requests import RegisterPatientRequest

from gc_dentist_shared.core.exception_handler.custom_exception import CustomValueError


class TestSingleRegisterPatientAPI:
    """Comprehensive unit tests for single_register_patient API endpoint."""

    @pytest.fixture
    def valid_patient_data(self):
        """Valid patient registration data for testing."""
        return {
            "clinic_code": "TEST_CLINIC_001",
            "patient_number": "P123456789",
            "full_name": "田中太郎",
            "full_name_kana": "タナカタロウ",
            "gender": "M",
            "date_of_birth": "1990-01-01",
            "telephone_number_1": "090-1234-5678",
            "telephone_number_2": "03-1234-5678",
            "postal_code": "123-4567",
            "address": "東京都渋谷区1-1-1"
        }

    @pytest.fixture
    def minimal_patient_data(self):
        """Minimal required patient data for testing."""
        return {
            "clinic_code": "TEST_CLINIC_001",
            "patient_number": "P123456789",
            "full_name": "田中太郎",
            "full_name_kana": "タナカタロウ",
            "date_of_birth": "1990-01-01"
        }

    @pytest.fixture
    def mock_tenant_clinic(self):
        """Mock tenant clinic data."""
        return MagicMock(
            tenant_uuid=uuid.uuid4(),
            db_name="test_clinic_db",
            clinic_code="TEST_CLINIC_001"
        )

    # Success Test Cases
    @pytest.mark.asyncio
    async def test_register_patient_success_with_full_data(
        self, async_client: AsyncClient, valid_patient_data, mock_tenant_clinic, bypass_api_key_check
    ):
        """Test successful patient registration with all fields provided."""
        with patch("services.patient_service.PatientService.register_patient") as mock_register:
            mock_register.return_value = None
            
            response = await async_client.post(
                "/v1_0/patients/register",
                json=valid_patient_data
            )
            
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert response_data["success"] is True
            assert response_data["message"] == CustomMessageCode.REGISTER_PATIENT_SUCCESS.title
            assert response_data["messageCode"] == CustomMessageCode.REGISTER_PATIENT_SUCCESS.code
            mock_register.assert_called_once()

    @pytest.mark.asyncio
    async def test_register_patient_success_with_minimal_data(
        self, async_client: AsyncClient, minimal_patient_data, mock_tenant_clinic, bypass_api_key_check
    ):
        """Test successful patient registration with only required fields."""
        with patch("services.patient_service.PatientService.register_patient") as mock_register:
            mock_register.return_value = None
            
            response = await async_client.post(
                "/v1_0/patients/register",
                json=minimal_patient_data
            )
            
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert response_data["success"] is True
            mock_register.assert_called_once()

    # Validation Error Test Cases
    @pytest.mark.asyncio
    async def test_register_patient_missing_required_fields(
        self, async_client: AsyncClient, bypass_api_key_check
    ):
        """Test validation error when required fields are missing."""
        invalid_data = {
            "clinic_code": "TEST_CLINIC_001"
            # Missing required fields: patient_number, full_name, full_name_kana, date_of_birth
        }
        
        response = await async_client.post(
            "/v1_0/patients/register",
            json=invalid_data
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        response_data = response.json()
        assert response_data["success"] is False
        assert "messageErrors" in response_data
        assert len(response_data["messageErrors"]) > 0

    @pytest.mark.asyncio
    async def test_register_patient_empty_patient_number(
        self, async_client: AsyncClient, valid_patient_data, bypass_api_key_check
    ):
        """Test validation error when patient_number is empty."""
        valid_patient_data["patient_number"] = ""
        
        response = await async_client.post(
            "/v1_0/patients/register",
            json=valid_patient_data
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        response_data = response.json()
        assert response_data["success"] is False
        # Should contain validation error about required patient number
        error_messages = response_data.get("messageErrors", [])
        assert any("patient_number" in msg.lower() for msg in error_messages)

    @pytest.mark.asyncio
    async def test_register_patient_patient_number_too_long(
        self, async_client: AsyncClient, valid_patient_data, bypass_api_key_check
    ):
        """Test validation error when patient_number exceeds maximum length."""
        valid_patient_data["patient_number"] = "P" + "1" * 35  # 36 characters, exceeds 30 limit
        
        response = await async_client.post(
            "/v1_0/patients/register",
            json=valid_patient_data
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        response_data = response.json()
        assert response_data["success"] is False

    @pytest.mark.asyncio
    async def test_register_patient_invalid_telephone_format(
        self, async_client: AsyncClient, valid_patient_data, bypass_api_key_check
    ):
        """Test validation error when telephone number has invalid format."""
        valid_patient_data["telephone_number_1"] = "invalid-phone-format"
        
        response = await async_client.post(
            "/v1_0/patients/register",
            json=valid_patient_data
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        response_data = response.json()
        assert response_data["success"] is False

    @pytest.mark.asyncio
    async def test_register_patient_telephone_too_long(
        self, async_client: AsyncClient, valid_patient_data, bypass_api_key_check
    ):
        """Test validation error when telephone number exceeds maximum length."""
        valid_patient_data["telephone_number_1"] = "090-1234-567890"  # 14 characters, exceeds 13 limit
        
        response = await async_client.post(
            "/v1_0/patients/register",
            json=valid_patient_data
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        response_data = response.json()
        assert response_data["success"] is False

    # Business Logic Error Test Cases
    @pytest.mark.asyncio
    async def test_register_patient_clinic_not_found(
        self, async_client: AsyncClient, valid_patient_data, bypass_api_key_check
    ):
        """Test error when clinic code is not found."""
        with patch("services.patient_service.PatientService.register_patient") as mock_register:
            mock_register.side_effect = CustomValueError(
                status_code=status.HTTP_404_NOT_FOUND,
                message_code=CustomMessageCode.TENANT_CLINIC_NOT_FOUND.code,
                message=CustomMessageCode.TENANT_CLINIC_NOT_FOUND.title
            )
            
            response = await async_client.post(
                "/v1_0/patients/register",
                json=valid_patient_data
            )
            
            assert response.status_code == status.HTTP_404_NOT_FOUND
            response_data = response.json()
            assert response_data["success"] is False
            assert response_data["messageCode"] == CustomMessageCode.TENANT_CLINIC_NOT_FOUND.code

    @pytest.mark.asyncio
    async def test_register_patient_duplicate_patient_number(
        self, async_client: AsyncClient, valid_patient_data, bypass_api_key_check
    ):
        """Test error when patient number already exists."""
        with patch("services.patient_service.PatientService.register_patient") as mock_register:
            mock_register.side_effect = CustomValueError(
                status_code=status.HTTP_409_CONFLICT,
                message_code=6001,  # Assuming this is the duplicate patient error code
                message="Patient with this number already exists"
            )
            
            response = await async_client.post(
                "/v1_0/patients/register",
                json=valid_patient_data
            )
            
            assert response.status_code == status.HTTP_409_CONFLICT
            response_data = response.json()
            assert response_data["success"] is False
            assert response_data["messageCode"] == 6001

    # Database Error Test Cases
    @pytest.mark.asyncio
    async def test_register_patient_database_error(
        self, async_client: AsyncClient, valid_patient_data, bypass_api_key_check
    ):
        """Test handling of unexpected database errors."""
        with patch("services.patient_service.PatientService.register_patient") as mock_register:
            mock_register.side_effect = Exception("Database connection failed")
            
            response = await async_client.post(
                "/v1_0/patients/register",
                json=valid_patient_data
            )
            
            assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

    # Edge Cases
    @pytest.mark.asyncio
    async def test_register_patient_with_none_optional_fields(
        self, async_client: AsyncClient, minimal_patient_data, bypass_api_key_check
    ):
        """Test registration with explicitly None optional fields."""
        minimal_patient_data.update({
            "gender": None,
            "telephone_number_1": None,
            "telephone_number_2": None,
            "postal_code": None,
            "address": None
        })
        
        with patch("services.patient_service.PatientService.register_patient") as mock_register:
            mock_register.return_value = None
            
            response = await async_client.post(
                "/v1_0/patients/register",
                json=minimal_patient_data
            )
            
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert response_data["success"] is True

    @pytest.mark.asyncio
    async def test_register_patient_with_empty_string_optional_fields(
        self, async_client: AsyncClient, minimal_patient_data, bypass_api_key_check
    ):
        """Test registration with empty string optional fields."""
        minimal_patient_data.update({
            "gender": "",
            "telephone_number_1": "",
            "telephone_number_2": "",
            "postal_code": "",
            "address": ""
        })
        
        with patch("services.patient_service.PatientService.register_patient") as mock_register:
            mock_register.return_value = None
            
            response = await async_client.post(
                "/v1_0/patients/register",
                json=minimal_patient_data
            )
            
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert response_data["success"] is True
